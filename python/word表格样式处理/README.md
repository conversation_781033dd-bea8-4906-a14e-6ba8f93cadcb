# Word表格 MCP服务

专门用于Word文档表格格式化操作的MCP服务，提供完整的表格美化和布局控制功能。

## 功能特性

### 📊 表格基础格式化工具
- **`format_table_tool`** - 使用边框、阴影和结构格式化表格
  - 支持设置标题行、边框样式、单元格阴影
  - 提供多种边框样式选择

### 🎨 表格美化工具
- **`set_table_cell_shading_tool`** - 为特定表格单元格应用阴影/填充
  - 支持十六进制颜色和颜色名称
  - 支持多种阴影模式
- **`apply_table_alternating_rows_tool`** - 为表格应用交替行颜色
  - 提高表格可读性
  - 可自定义奇偶行颜色
- **`highlight_table_header_tool`** - 为表格标题行应用特殊高亮
  - 可自定义背景色和文字颜色

### 🔧 表格对齐与布局工具
- **`set_table_cell_alignment_tool`** - 设置特定表格单元格的文本对齐
  - 支持水平对齐：左对齐、居中、右对齐、两端对齐
  - 支持垂直对齐：顶部、居中、底部
- **`set_entire_table_alignment_tool`** - 设置整个表格所有单元格的文本对齐方式
  - 批量设置所有单元格的对齐方式
- **`set_table_column_width_tool`** - 设置特定表格列的宽度
  - 支持多种单位：磅、英寸、厘米、百分比、自动

## 工具详细说明

### 表格基础格式化
```json
{
  "filename": "document.docx",
  "table_index": 0,
  "has_header_row": true,
  "border_style": "single"
}
```

### 单元格阴影设置
```json
{
  "filename": "document.docx",
  "table_index": 0,
  "row_index": 0,
  "col_index": 0,
  "fill_color": "FF0000",
  "pattern": "clear"
}
```

### 表格对齐设置
```json
{
  "filename": "document.docx",
  "table_index": 0,
  "horizontal": "center",
  "vertical": "center"
}
```

### 列宽设置
```json
{
  "filename": "document.docx",
  "table_index": 0,
  "col_index": 0,
  "width": 2.0,
  "width_type": "inches"
}
```

## 使用示例

- "格式化第一个表格，添加边框和标题行"
- "设置表格第一行为蓝色背景"
- "设置整个表格文本居中对齐"
- "设置表格第一列宽度为2英寸"
- "为表格应用交替行颜色"
- "高亮表格标题行为蓝色背景白色文字"

## 技术特性

### 🔧 核心功能
- **表格美化**：完整的表格格式化解决方案
- **精确控制**：支持单个单元格和整个表格的格式化
- **批量操作**：支持整个表格的批量设置
- **灵活布局**：多种对齐和尺寸控制选项

### 📋 支持的格式
- **边框样式**：无边框、单线、双线、粗线
- **阴影模式**：清除、实心、百分比填充等
- **对齐方式**：水平（左/中/右/两端）、垂直（上/中/下）
- **尺寸单位**：磅、英寸、厘米、百分比、自动
- **颜色格式**：十六进制颜色值

### 🛡️ 安全特性
- **文件检查**：自动验证文件存在性和可写性
- **参数验证**：严格的输入参数类型和范围检查
- **索引验证**：自动验证表格、行、列索引的有效性
- **错误处理**：完整的异常捕获和友好的错误信息
- **备份建议**：在无法写入时提示创建副本

## 安装与运行

### 依赖要求
```bash
pip install python-docx fastmcp
```

### 启动服务
```bash
python -m word_table.main
```

### 配置说明
- 服务使用 stdio 传输协议
- 支持 FastMCP 框架
- 自动设置日志级别为 INFO

## 注意事项

1. **文件格式**：仅支持 .docx 格式的 Word 文档
2. **文件权限**：确保对目标文档有读写权限
3. **索引从0开始**：所有索引参数（表格、行、列）都从0开始计数
4. **颜色格式**：主要支持十六进制值（如 "FF0000"）
5. **单位转换**：自动处理不同单位之间的转换
6. **备份建议**：建议在格式化前备份重要文档

## 与其他服务的关系

本服务专注于表格格式化，与Word样式文本服务互补：
- **表格服务**：处理表格相关的所有格式化功能
- **样式文本服务**：处理样式创建和文本格式化

两个服务可以独立使用，也可以配合使用来完成复杂的Word文档格式化任务。
