[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "word-table-mcp"
version = "1.0.0"
description = "专门用于Word文档表格格式化的MCP服务"
authors = [
    {name = "Word格式化团队", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.8"
dependencies = [
    "python-docx>=1.1.0",
    "fastmcp>=0.2.0"
]

[project.scripts]
word-table-server = "word_table.main:main"

[project.entry-points."fastmcp.servers"]
word-table = "word_table.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["word_table*"]

[tool.setuptools.package-data]
word_table = ["*.py"]
