"""
Word表格 MCP服务主程序

专门提供Word文档的表格格式化功能。
"""
import os
import sys

# 设置FastMCP所需的环境变量
os.environ.setdefault('FASTMCP_LOG_LEVEL', 'INFO')

from fastmcp import FastMCP
from .tools import (
    format_table,
    set_table_cell_shading,
    apply_table_alternating_rows,
    highlight_table_header,
    set_table_cell_alignment,
    set_table_column_width,
    set_entire_table_alignment
)


# 初始化FastMCP服务器
mcp = FastMCP("Word表格服务")

def register_tools():
    """使用FastMCP装饰器注册所有工具"""

    @mcp.tool()
    def format_table_tool(filename: str, table_index: int, has_header_row: bool = None,
                         border_style: str = None, shading: list = None):
        """使用边框、阴影和结构格式化表格
        
        Args:
            filename: Word文档路径
            table_index: 表格索引（从0开始）
            has_header_row: 如果为True，将第一行格式化为标题
            border_style: 边框样式 ('none', 'single', 'double', 'thick')
            shading: 单元格背景颜色的二维列表（按行和列）
        """
        return format_table(filename, table_index, has_header_row, border_style, shading)

    @mcp.tool()
    def apply_table_alternating_rows_tool(filename: str, table_index: int,
                                         color1: str = "FFFFFF", color2: str = "F2F2F2"):
        """为表格应用交替行颜色以提高可读性
        
        Args:
            filename: Word文档路径
            table_index: 表格索引（从0开始）
            color1: 奇数行颜色（十六进制字符串，默认白色）
            color2: 偶数行颜色（十六进制字符串，默认浅灰色）
        """
        return apply_table_alternating_rows(filename, table_index, color1, color2)

    @mcp.tool()
    def highlight_table_header_tool(filename: str, table_index: int,
                                   header_color: str = "4472C4", text_color: str = "FFFFFF"):
        """为表格标题行应用特殊高亮
        
        Args:
            filename: Word文档路径
            table_index: 表格索引（从0开始）
            header_color: 标题背景颜色（十六进制字符串，默认蓝色）
            text_color: 标题文本颜色（十六进制字符串，默认白色）
        """
        return highlight_table_header(filename, table_index, header_color, text_color)

    @mcp.tool()
    def set_table_cell_alignment_tool(filename: str, table_index: int, row_index: int, col_index: int,
                                     horizontal: str = "left", vertical: str = "top"):
        """设置特定表格单元格的文本对齐
        
        Args:
            filename: Word文档路径
            table_index: 表格索引（从0开始）
            row_index: 行索引（从0开始）
            col_index: 列索引（从0开始）
            horizontal: 水平对齐 ("left", "center", "right", "justify")
            vertical: 垂直对齐 ("top", "center", "bottom")
        """
        return set_table_cell_alignment(filename, table_index, row_index, col_index, horizontal, vertical)

    @mcp.tool()
    def set_table_cell_shading_tool(filename: str, table_index: int, row_index: int,
                                   col_index: int, fill_color: str, pattern: str = "clear"):
        """为特定表格单元格应用阴影/填充
        
        Args:
            filename: Word文档路径
            table_index: 表格索引（从0开始）
            row_index: 单元格行索引（从0开始）
            col_index: 单元格列索引（从0开始）
            fill_color: 背景颜色（十六进制字符串如"FF0000"或颜色名如"red"）
            pattern: 阴影模式 ("clear", "solid", "pct10", "pct20"等)
        """
        return set_table_cell_shading(filename, table_index, row_index, col_index, fill_color, pattern)

    @mcp.tool()
    def set_table_column_width_tool(filename: str, table_index: int, col_index: int,
                                   width: float, width_type: str = "points"):
        """设置特定表格列的宽度
        
        Args:
            filename: Word文档路径
            table_index: 表格索引（从0开始）
            col_index: 列索引（从0开始）
            width: 宽度值
            width_type: 宽度类型 ("points", "inches", "cm", "percent", "auto")
        """
        return set_table_column_width(filename, table_index, col_index, width, width_type)

    @mcp.tool()
    def set_entire_table_alignment_tool(filename: str, table_index: int,
                                       horizontal: str = "left", vertical: str = "top"):
        """设置整个表格所有单元格的文本对齐方式
        
        Args:
            filename: Word文档路径
            table_index: 表格索引（从0开始）
            horizontal: 水平对齐 ("left", "center", "right", "justify")
            vertical: 垂直对齐 ("top", "center", "bottom")
        """
        return set_entire_table_alignment(filename, table_index, horizontal, vertical)

def main():
    """服务器的主入口点 - 只支持stdio传输"""
    # 注册所有工具
    register_tools()

    print("启动Word表格MCP服务器...")

    try:
        # 只使用stdio传输运行
        mcp.run(transport='stdio')
    except KeyboardInterrupt:
        print("\n正在关闭Word表格服务器...")
    except Exception as e:
        print(f"启动服务器时出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
