"""
Word表格工具 - 提供表格格式化功能
"""
import os
from typing import List, Optional
from docx import Document

from .utils import (
    check_file_writeable,
    ensure_docx_extension,
    apply_table_style,
    apply_alternating_row_shading,
    highlight_header_row,
    set_cell_shading,
    set_cell_alignment,
    set_table_alignment,
    set_column_width
)


def format_table(filename: str, table_index: int,
                      has_header_row: Optional[bool] = None,
                      border_style: Optional[str] = None,
                      shading: Optional[List[List[str]]] = None) -> str:
    """
    使用边框、阴影和结构格式化表格
    
    Args:
        filename: Word文档路径
        table_index: 表格索引（从0开始）
        has_header_row: 如果为True，将第一行格式化为标题
        border_style: 边框样式 ('none', 'single', 'double', 'thick')
        shading: 单元格背景颜色的二维列表（按行和列）
    """
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"
    
    try:
        doc = Document(filename)
        
        # 验证表格索引
        if table_index < 0 or table_index >= len(doc.tables):
            return f"无效的表格索引。文档有 {len(doc.tables)} 个表格 (0-{len(doc.tables)-1})。"
        
        table = doc.tables[table_index]
        
        # 应用格式化
        success = apply_table_style(table, has_header_row or False, border_style, shading)
        
        if success:
            doc.save(filename)
            return f"索引 {table_index} 处的表格格式化成功。"
        else:
            return f"格式化索引 {table_index} 处的表格失败。"
    except Exception as e:
        return f"格式化表格失败: {str(e)}"


def set_table_cell_shading(filename: str, table_index: int, row_index: int,
                                col_index: int, fill_color: str, pattern: str = "clear") -> str:
    """
    为特定表格单元格应用阴影/填充
    
    Args:
        filename: Word文档路径
        table_index: 表格索引（从0开始）
        row_index: 单元格行索引（从0开始）
        col_index: 单元格列索引（从0开始）
        fill_color: 背景颜色（十六进制字符串如"FF0000"或颜色名如"red"）
        pattern: 阴影模式 ("clear", "solid", "pct10", "pct20"等)
    """
    filename = ensure_docx_extension(filename)
    
    # 确保数值参数是正确的类型
    try:
        table_index = int(table_index)
        row_index = int(row_index)
        col_index = int(col_index)
    except (ValueError, TypeError):
        return "无效参数: table_index, row_index, 和 col_index 必须是整数"
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"
    
    try:
        doc = Document(filename)
        
        # 验证表格索引
        if table_index < 0 or table_index >= len(doc.tables):
            return f"无效的表格索引。文档有 {len(doc.tables)} 个表格 (0-{len(doc.tables)-1})。"
        
        table = doc.tables[table_index]
        
        # 验证行和列索引
        if row_index < 0 or row_index >= len(table.rows):
            return f"无效的行索引。表格有 {len(table.rows)} 行 (0-{len(table.rows)-1})。"
        
        if col_index < 0 or col_index >= len(table.columns):
            return f"无效的列索引。表格有 {len(table.columns)} 列 (0-{len(table.columns)-1})。"
        
        cell = table.cell(row_index, col_index)
        
        # 应用单元格阴影
        set_cell_shading(cell, fill_color=fill_color, pattern=pattern)
        
        doc.save(filename)
        return f"单元格阴影应用成功到表格 {table_index}，单元格 ({row_index},{col_index})。"
    except Exception as e:
        return f"应用单元格阴影失败: {str(e)}"


def apply_table_alternating_rows(filename: str, table_index: int,
                                     color1: str = "FFFFFF", color2: str = "F2F2F2") -> str:
    """
    为表格应用交替行颜色以提高可读性

    Args:
        filename: Word文档路径
        table_index: 表格索引（从0开始）
        color1: 奇数行颜色（十六进制字符串，默认白色）
        color2: 偶数行颜色（十六进制字符串，默认浅灰色）
    """
    filename = ensure_docx_extension(filename)

    # 确保数值参数是正确的类型
    try:
        table_index = int(table_index)
    except (ValueError, TypeError):
        return "无效参数: table_index 必须是整数"

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"

    try:
        doc = Document(filename)

        # 验证表格索引
        if table_index < 0 or table_index >= len(doc.tables):
            return f"无效的表格索引。文档有 {len(doc.tables)} 个表格 (0-{len(doc.tables)-1})。"

        table = doc.tables[table_index]

        # 应用交替行阴影
        success = apply_alternating_row_shading(table, color1, color2)

        if success:
            doc.save(filename)
            return f"交替行颜色应用成功到表格 {table_index}。"
        else:
            return f"应用交替行颜色失败。"
    except Exception as e:
        return f"应用交替行颜色失败: {str(e)}"


def highlight_table_header(filename: str, table_index: int,
                               header_color: str = "4472C4", text_color: str = "FFFFFF") -> str:
    """
    为表格标题行应用特殊高亮

    Args:
        filename: Word文档路径
        table_index: 表格索引（从0开始）
        header_color: 标题背景颜色（十六进制字符串，默认蓝色）
        text_color: 标题文本颜色（十六进制字符串，默认白色）
    """
    filename = ensure_docx_extension(filename)

    # 确保数值参数是正确的类型
    try:
        table_index = int(table_index)
    except (ValueError, TypeError):
        return "无效参数: table_index 必须是整数"

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"

    try:
        doc = Document(filename)

        # 验证表格索引
        if table_index < 0 or table_index >= len(doc.tables):
            return f"无效的表格索引。文档有 {len(doc.tables)} 个表格 (0-{len(doc.tables)-1})。"

        table = doc.tables[table_index]

        # 应用标题高亮
        success = highlight_header_row(table, header_color, text_color)

        if success:
            doc.save(filename)
            return f"标题高亮应用成功到表格 {table_index}。"
        else:
            return f"应用标题高亮失败。"
    except Exception as e:
        return f"应用标题高亮失败: {str(e)}"


def set_table_cell_alignment(filename: str, table_index: int, row_index: int, col_index: int,
                                 horizontal: str = "left", vertical: str = "top") -> str:
    """
    设置特定表格单元格的文本对齐

    Args:
        filename: Word文档路径
        table_index: 表格索引（从0开始）
        row_index: 行索引（从0开始）
        col_index: 列索引（从0开始）
        horizontal: 水平对齐 ("left", "center", "right", "justify")
        vertical: 垂直对齐 ("top", "center", "bottom")
    """
    filename = ensure_docx_extension(filename)

    # 确保数值参数是正确的类型
    try:
        table_index = int(table_index)
        row_index = int(row_index)
        col_index = int(col_index)
    except (ValueError, TypeError):
        return "无效参数: table_index, row_index, 和 col_index 必须是整数"

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"

    try:
        doc = Document(filename)

        # 验证表格索引
        if table_index < 0 or table_index >= len(doc.tables):
            return f"无效的表格索引。文档有 {len(doc.tables)} 个表格 (0-{len(doc.tables)-1})。"

        table = doc.tables[table_index]

        # 验证行和列索引
        if row_index < 0 or row_index >= len(table.rows):
            return f"无效的行索引。表格有 {len(table.rows)} 行 (0-{len(table.rows)-1})。"

        if col_index < 0 or col_index >= len(table.columns):
            return f"无效的列索引。表格有 {len(table.columns)} 列 (0-{len(table.columns)-1})。"

        cell = table.cell(row_index, col_index)

        # 设置单元格对齐
        set_cell_alignment(cell, horizontal, vertical)

        doc.save(filename)
        return f"单元格对齐设置成功，表格 {table_index}，单元格 ({row_index},{col_index}) 设为 {horizontal}/{vertical}。"
    except Exception as e:
        return f"设置单元格对齐失败: {str(e)}"


def set_table_column_width(filename: str, table_index: int, col_index: int,
                                width: float, width_type: str = "points") -> str:
    """
    设置特定表格列的宽度

    Args:
        filename: Word文档路径
        table_index: 表格索引（从0开始）
        col_index: 列索引（从0开始）
        width: 宽度值
        width_type: 宽度类型 ("points", "inches", "cm", "percent", "auto")
    """
    filename = ensure_docx_extension(filename)

    # 确保数值参数是正确的类型
    try:
        table_index = int(table_index)
        col_index = int(col_index)
        if width_type.lower() != "auto":
            width = float(width)
    except (ValueError, TypeError):
        return "无效参数: table_index 和 col_index 必须是整数，width 必须是数字"

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"

    try:
        doc = Document(filename)

        # 验证表格索引
        if table_index < 0 or table_index >= len(doc.tables):
            return f"无效的表格索引。文档有 {len(doc.tables)} 个表格 (0-{len(doc.tables)-1})。"

        table = doc.tables[table_index]

        # 验证列索引
        if col_index < 0 or col_index >= len(table.columns):
            return f"无效的列索引。表格有 {len(table.columns)} 列 (0-{len(table.columns)-1})。"

        # 转换宽度和类型为Word格式
        if width_type.lower() == "points":
            word_width = width
            word_type = "dxa"
        elif width_type.lower() == "inches":
            word_width = width * 72  # 每英寸72磅
            word_type = "dxa"
        elif width_type.lower() == "cm":
            word_width = width * 28.35  # 每厘米约28.35磅
            word_type = "dxa"
        elif width_type.lower() == "percent":
            word_width = width
            word_type = "pct"
        else:  # auto
            word_width = 0
            word_type = "auto"

        # 设置列宽
        success = set_column_width(table, col_index, word_width, word_type)

        if success:
            doc.save(filename)
            return f"列宽设置成功，表格 {table_index}，列 {col_index} 设为 {width} {width_type}。"
        else:
            return f"设置列宽失败。"
    except Exception as e:
        return f"设置列宽失败: {str(e)}"


def set_entire_table_alignment(filename: str, table_index: int,
                               horizontal: str = "left", vertical: str = "top") -> str:
    """
    设置整个表格所有单元格的文本对齐方式

    Args:
        filename: Word文档路径
        table_index: 表格索引（从0开始）
        horizontal: 水平对齐 ("left", "center", "right", "justify")
        vertical: 垂直对齐 ("top", "center", "bottom")
    """
    filename = ensure_docx_extension(filename)

    # 确保数值参数是正确的类型
    try:
        table_index = int(table_index)
    except (ValueError, TypeError):
        return "无效参数: table_index 必须是整数"

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"

    try:
        doc = Document(filename)

        # 验证表格索引
        if table_index < 0 or table_index >= len(doc.tables):
            return f"无效的表格索引。文档有 {len(doc.tables)} 个表格 (0-{len(doc.tables)-1})。"

        table = doc.tables[table_index]

        # 设置整个表格的对齐
        success = set_table_alignment(table, horizontal, vertical)

        if success:
            doc.save(filename)
            return f"整个表格 {table_index} 的文本对齐设置成功，设为 {horizontal}/{vertical}。"
        else:
            return f"设置整个表格对齐失败。"
    except Exception as e:
        return f"设置整个表格对齐失败: {str(e)}"
