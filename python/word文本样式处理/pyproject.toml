[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "word-style-text-mcp"
version = "1.0.0"
description = "专门用于Word文档样式创建和文本格式化的MCP服务"
authors = [
    {name = "Word格式化团队", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.8"
dependencies = [
    "python-docx>=1.1.0",
    "fastmcp>=0.2.0"
]

[project.scripts]
word-style-text-server = "word_style_text.main:main"

[project.entry-points."fastmcp.servers"]
word-style-text = "word_style_text.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["word_style_text*"]

[tool.setuptools.package-data]
word_style_text = ["*.py"]
