# Word样式文本 MCP服务

专门用于Word文档样式创建和文本格式化操作的MCP服务。

## 功能特性

### 🎨 样式创建工具
- **`create_custom_style_tool`** - 创建自定义样式
  - 支持设置加粗、斜体、字体大小、字体名称、颜色等属性
  - 可基于现有样式创建新样式
  - 支持段落样式和字符样式

### ✏️ 文本格式化工具
- **`format_text_tool`** - 格式化段落中的特定文本范围
  - 支持加粗、斜体、下划线、颜色、字体大小、字体名称等格式
  - 精确的位置控制，可以格式化段落中的任意文本范围
  - 支持多种颜色格式（颜色名称和十六进制值）

## 工具详细说明

### 样式创建
```json
{
  "filename": "document.docx",
  "style_name": "重要提示",
  "bold": true,
  "color": "red",
  "font_size": 14,
  "font_name": "微软雅黑"
}
```

### 文本格式化
```json
{
  "filename": "document.docx",
  "paragraph_index": 1,
  "start_pos": 10,
  "end_pos": 20,
  "bold": true,
  "color": "blue",
  "font_size": 12
}
```

## 使用示例

- "创建名为'重要提示'的自定义样式，加粗红色字体"
- "将第2段第10-20字符设为加粗蓝色"
- "创建基于'标题1'的新样式'自定义标题'"
- "将第1段第5-15字符设为斜体绿色，字体大小16磅"

## 技术特性

### 🔧 核心功能
- **样式管理**：创建和应用自定义样式
- **精确格式化**：基于位置的文本格式化
- **颜色支持**：支持颜色名称和十六进制颜色值
- **字体控制**：完整的字体属性控制

### 📋 支持的格式
- **文本格式**：加粗、斜体、下划线、颜色、字体大小、字体名称
- **颜色格式**：red, blue, green, yellow, black, gray, white, purple, orange
- **字体单位**：磅（points）
- **样式类型**：段落样式、字符样式

### 🛡️ 安全特性
- **文件检查**：自动验证文件存在性和可写性
- **参数验证**：严格的输入参数类型和范围检查
- **错误处理**：完整的异常捕获和友好的错误信息
- **备份建议**：在无法写入时提示创建副本

## 安装与运行

### 依赖要求
```bash
pip install python-docx fastmcp
```

### 启动服务
```bash
python -m word_style_text.main
```

### 配置说明
- 服务使用 stdio 传输协议
- 支持 FastMCP 框架
- 自动设置日志级别为 INFO

## 注意事项

1. **文件格式**：仅支持 .docx 格式的 Word 文档
2. **文件权限**：确保对目标文档有读写权限
3. **索引从0开始**：所有索引参数（段落）都从0开始计数
4. **位置精确性**：文本位置基于段落内的字符位置
5. **颜色格式**：支持颜色名称（如 "red"）和十六进制值（如 "FF0000"）
6. **备份建议**：建议在格式化前备份重要文档

## 与其他服务的关系

本服务专注于样式和文本格式化，与Word表格服务互补：
- **样式文本服务**：处理样式创建和文本格式化
- **表格服务**：处理表格相关的所有格式化功能

两个服务可以独立使用，也可以配合使用来完成复杂的Word文档格式化任务。
