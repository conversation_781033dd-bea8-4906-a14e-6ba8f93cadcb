"""
Word样式文本工具 - 提供样式创建和文本格式化功能
"""
import os
from typing import Optional
from docx import Document
from docx.shared import Pt, RGBColor
from docx.enum.style import WD_STYLE_TYPE

from .utils import (
    check_file_writeable,
    ensure_docx_extension,
    create_style
)


def create_custom_style(filename: str, style_name: str,
                             bold: Optional[bool] = None, italic: Optional[bool] = None,
                             font_size: Optional[int] = None, font_name: Optional[str] = None,
                             color: Optional[str] = None, base_style: Optional[str] = None) -> str:
    """
    在文档中创建自定义样式
    
    Args:
        filename: Word文档路径
        style_name: 新样式的名称
        bold: 设置文本加粗 (True/False)
        italic: 设置文本斜体 (True/False)
        font_size: 字体大小（磅）
        font_name: 字体名称/系列
        color: 文本颜色（例如：'red', 'blue'）
        base_style: 可选的现有样式作为基础
    """
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"
    
    try:
        doc = Document(filename)
        
        # 构建字体属性字典
        font_properties = {}
        if bold is not None:
            font_properties['bold'] = bold
        if italic is not None:
            font_properties['italic'] = italic
        if font_size is not None:
            font_properties['size'] = font_size
        if font_name is not None:
            font_properties['name'] = font_name
        if color is not None:
            font_properties['color'] = color
        
        # 创建样式
        new_style = create_style(
            doc, 
            style_name, 
            WD_STYLE_TYPE.PARAGRAPH, 
            base_style=base_style,
            font_properties=font_properties
        )
        
        doc.save(filename)
        return f"样式 '{style_name}' 创建成功。"
    except Exception as e:
        return f"创建样式失败: {str(e)}"


def format_text(filename: str, paragraph_index: int, start_pos: int, end_pos: int,
                     bold: Optional[bool] = None, italic: Optional[bool] = None,
                     underline: Optional[bool] = None, color: Optional[str] = None,
                     font_size: Optional[int] = None, font_name: Optional[str] = None) -> str:
    """
    格式化段落中的特定文本范围
    
    Args:
        filename: Word文档路径
        paragraph_index: 段落索引（从0开始）
        start_pos: 段落文本中的开始位置
        end_pos: 段落文本中的结束位置
        bold: 设置文本加粗 (True/False)
        italic: 设置文本斜体 (True/False)
        underline: 设置文本下划线 (True/False)
        color: 文本颜色（例如：'red', 'blue'等）
        font_size: 字体大小（磅）
        font_name: 字体名称/系列
    """
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"
    
    try:
        doc = Document(filename)
        
        # 验证段落索引
        if paragraph_index < 0 or paragraph_index >= len(doc.paragraphs):
            return f"无效的段落索引。文档有 {len(doc.paragraphs)} 个段落 (0-{len(doc.paragraphs)-1})。"
        
        paragraph = doc.paragraphs[paragraph_index]
        text = paragraph.text
        
        # 验证位置
        if start_pos < 0 or end_pos > len(text) or start_pos >= end_pos:
            return f"无效的文本位置。段落长度: {len(text)}, 请求范围: {start_pos}-{end_pos}"
        
        target_text = text[start_pos:end_pos]
        
        # 清除现有运行并重新创建
        paragraph.clear()
        
        # 添加目标前的文本
        if start_pos > 0:
            paragraph.add_run(text[:start_pos])
        
        # 添加格式化的目标文本
        run_target = paragraph.add_run(target_text)
        
        # 应用格式化
        if bold is not None:
            run_target.bold = bold
        if italic is not None:
            run_target.italic = italic
        if underline is not None:
            run_target.underline = underline
        
        # 处理颜色
        if color:
            # 定义常见RGB颜色
            color_map = {
                'red': RGBColor(255, 0, 0),
                'blue': RGBColor(0, 0, 255),
                'green': RGBColor(0, 128, 0),
                'yellow': RGBColor(255, 255, 0),
                'black': RGBColor(0, 0, 0),
                'gray': RGBColor(128, 128, 128),
                'white': RGBColor(255, 255, 255),
                'purple': RGBColor(128, 0, 128),
                'orange': RGBColor(255, 165, 0)
            }
            
            try:
                if color.lower() in color_map:
                    # 使用预定义的RGB颜色
                    run_target.font.color.rgb = color_map[color.lower()]
                else:
                    # 尝试按名称设置颜色
                    run_target.font.color.rgb = RGBColor.from_string(color)
            except Exception as e:
                # 如果所有方法都失败，默认为黑色
                run_target.font.color.rgb = RGBColor(0, 0, 0)
        
        if font_size:
            run_target.font.size = Pt(font_size)
        if font_name:
            run_target.font.name = font_name
        
        # 添加目标后的文本
        if end_pos < len(text):
            paragraph.add_run(text[end_pos:])
        
        doc.save(filename)
        return f"文本 '{target_text}' 在段落 {paragraph_index} 中格式化成功。"
    except Exception as e:
        return f"格式化文本失败: {str(e)}"
