"""
样式文本工具类 - 提供文件操作和样式文本格式化的基础功能
"""
import os
from typing import Dict, List, Any, Tuple, Optional
from docx import Document
from docx.shared import Pt, RGBColor
from docx.enum.style import WD_STYLE_TYPE


def check_file_writeable(filepath: str) -> Tuple[bool, str]:
    """
    检查文件是否可写
    
    Args:
        filepath: 文件路径
        
    Returns:
        Tuple of (is_writeable, error_message)
    """
    # 如果文件不存在，检查目录是否可写
    if not os.path.exists(filepath):
        directory = os.path.dirname(filepath)
        # 如果没有指定目录，使用当前目录
        if directory == '':
            directory = '.'
        if not os.path.exists(directory):
            return False, f"目录 {directory} 不存在"
        if not os.access(directory, os.W_OK):
            return False, f"目录 {directory} 不可写"
        return True, ""
    
    # 如果文件存在，检查是否可写
    if not os.access(filepath, os.W_OK):
        return False, f"文件 {filepath} 不可写（权限被拒绝）"
    
    # 尝试打开文件进行写入，检查是否被锁定
    try:
        with open(filepath, 'a'):
            pass
        return True, ""
    except IOError as e:
        return False, f"文件 {filepath} 不可写: {str(e)}"
    except Exception as e:
        return False, f"检查文件权限时出现未知错误: {str(e)}"


def ensure_docx_extension(filename: str) -> str:
    """
    确保文件名具有.docx扩展名
    
    Args:
        filename: 要检查的文件名
        
    Returns:
        带有.docx扩展名的文件名
    """
    if not filename.endswith('.docx'):
        return filename + '.docx'
    return filename


def create_style(doc, style_name, style_type, base_style=None, font_properties=None, paragraph_properties=None):
    """
    在文档中创建新样式
    
    Args:
        doc: Document对象
        style_name: 新样式的名称
        style_type: 样式类型 (WD_STYLE_TYPE)
        base_style: 可选的基础样式继承
        font_properties: 字体属性字典 (bold, italic, size, name, color)
        paragraph_properties: 段落属性字典 (alignment, spacing)
        
    Returns:
        创建的样式
    """
    try:
        # 检查样式是否已存在
        style = doc.styles.get_by_id(style_name, WD_STYLE_TYPE.PARAGRAPH)
        return style
    except:
        # 创建新样式
        new_style = doc.styles.add_style(style_name, style_type)
        
        # 设置基础样式
        if base_style:
            new_style.base_style = doc.styles[base_style]
        
        # 设置字体属性
        if font_properties:
            font = new_style.font
            if 'bold' in font_properties:
                font.bold = font_properties['bold']
            if 'italic' in font_properties:
                font.italic = font_properties['italic']
            if 'size' in font_properties:
                font.size = Pt(font_properties['size'])
            if 'name' in font_properties:
                font.name = font_properties['name']
            if 'color' in font_properties:
                # 定义常见RGB颜色
                color_map = {
                    'red': RGBColor(255, 0, 0),
                    'blue': RGBColor(0, 0, 255),
                    'green': RGBColor(0, 128, 0),
                    'yellow': RGBColor(255, 255, 0),
                    'black': RGBColor(0, 0, 0),
                    'gray': RGBColor(128, 128, 128),
                    'white': RGBColor(255, 255, 255),
                    'purple': RGBColor(128, 0, 128),
                    'orange': RGBColor(255, 165, 0)
                }
                
                color_value = font_properties['color']
                try:
                    # 处理字符串颜色名称
                    if isinstance(color_value, str) and color_value.lower() in color_map:
                        font.color.rgb = color_map[color_value.lower()]
                    # 处理RGBColor对象
                    elif hasattr(color_value, 'rgb'):
                        font.color.rgb = color_value
                    # 尝试解析为RGB字符串
                    elif isinstance(color_value, str):
                        font.color.rgb = RGBColor.from_string(color_value)
                    # 如果已经是RGB值则直接使用
                    else:
                        font.color.rgb = color_value
                except Exception as e:
                    # 如果所有方法都失败，回退到黑色
                    font.color.rgb = RGBColor(0, 0, 0)
        
        # 设置段落属性
        if paragraph_properties:
            if 'alignment' in paragraph_properties:
                new_style.paragraph_format.alignment = paragraph_properties['alignment']
            if 'spacing' in paragraph_properties:
                new_style.paragraph_format.line_spacing = paragraph_properties['spacing']
        
        return new_style
