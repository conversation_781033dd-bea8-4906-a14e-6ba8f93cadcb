"""
Word样式文本 MCP服务主程序

专门提供Word文档的样式创建和文本格式化功能。
"""
import os
import sys

# 设置FastMCP所需的环境变量
os.environ.setdefault('FASTMCP_LOG_LEVEL', 'INFO')

from fastmcp import FastMCP
from .tools import (
    create_custom_style,
    format_text
)


# 初始化FastMCP服务器
mcp = FastMCP("Word样式文本服务")

def register_tools():
    """使用FastMCP装饰器注册所有工具"""

    @mcp.tool()
    def create_custom_style_tool(filename: str, style_name: str, bold: bool = None,
                                italic: bool = None, font_size: int = None,
                                font_name: str = None, color: str = None,
                                base_style: str = None):
        """在文档中创建自定义样式
        
        Args:
            filename: Word文档路径
            style_name: 新样式的名称
            bold: 设置文本加粗 (True/False)
            italic: 设置文本斜体 (True/False)
            font_size: 字体大小（磅）
            font_name: 字体名称/系列
            color: 文本颜色（例如：'red', 'blue'）
            base_style: 可选的现有样式作为基础
        """
        return create_custom_style(filename, style_name, bold, italic, font_size, font_name, color, base_style)

    @mcp.tool()
    def format_text_tool(filename: str, paragraph_index: int, start_pos: int, end_pos: int,
                        bold: bool = None, italic: bool = None, underline: bool = None,
                        color: str = None, font_size: int = None, font_name: str = None):
        """格式化段落中的特定文本范围
        
        Args:
            filename: Word文档路径
            paragraph_index: 段落索引（从0开始）
            start_pos: 段落文本中的开始位置
            end_pos: 段落文本中的结束位置
            bold: 设置文本加粗 (True/False)
            italic: 设置文本斜体 (True/False)
            underline: 设置文本下划线 (True/False)
            color: 文本颜色（例如：'red', 'blue'等）
            font_size: 字体大小（磅）
            font_name: 字体名称/系列
        """
        return format_text(filename, paragraph_index, start_pos, end_pos, bold, italic, underline, color, font_size, font_name)

def main():
    """服务器的主入口点 - 只支持stdio传输"""
    # 注册所有工具
    register_tools()

    print("启动Word样式文本MCP服务器...")

    try:
        # 只使用stdio传输运行
        mcp.run(transport='stdio')
    except KeyboardInterrupt:
        print("\n正在关闭Word样式文本服务器...")
    except Exception as e:
        print(f"启动服务器时出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
