#!/usr/bin/env python3
"""
Word格式化服务安装脚本

用于安装Word样式文本服务和Word表格服务
"""
import os
import sys
import subprocess
from pathlib import Path


def run_command(command, cwd=None):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd,
            capture_output=True, 
            text=True, 
            check=True
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr


def install_service(service_path, service_name):
    """安装指定的服务"""
    print(f"\n🔧 正在安装 {service_name}...")
    
    if not os.path.exists(service_path):
        print(f"❌ 错误: 服务路径 {service_path} 不存在")
        return False
    
    # 安装服务
    success, output = run_command("pip install -e .", cwd=service_path)
    
    if success:
        print(f"✅ {service_name} 安装成功")
        return True
    else:
        print(f"❌ {service_name} 安装失败:")
        print(output)
        return False


def main():
    """主安装函数"""
    print("🚀 Word格式化服务安装程序")
    print("=" * 50)
    
    # 获取当前脚本所在目录
    script_dir = Path(__file__).parent
    
    # 定义服务路径
    services = [
        {
            "name": "Word样式文本服务",
            "path": script_dir / "word文本样式处理"
        },
        {
            "name": "Word表格服务", 
            "path": script_dir / "word表格样式处理"
        }
    ]
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    
    print(f"✅ Python版本: {sys.version}")
    
    # 安装基础依赖
    print("\n📦 安装基础依赖...")
    base_deps = ["python-docx>=1.1.0", "fastmcp>=0.2.0"]
    
    for dep in base_deps:
        print(f"安装 {dep}...")
        success, output = run_command(f"pip install {dep}")
        if not success:
            print(f"❌ 安装 {dep} 失败: {output}")
            sys.exit(1)
    
    print("✅ 基础依赖安装完成")
    
    # 安装各个服务
    success_count = 0
    for service in services:
        if install_service(service["path"], service["name"]):
            success_count += 1
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 安装总结:")
    print(f"✅ 成功安装: {success_count}/{len(services)} 个服务")
    
    if success_count == len(services):
        print("\n🎉 所有服务安装成功!")
        print("\n📖 使用方法:")
        print("启动样式文本服务: python -m word_style_text.main")
        print("启动表格服务: python -m word_table.main")
        print("\n📚 查看各服务的README.md文件了解详细使用方法")
    else:
        print(f"\n⚠️  有 {len(services) - success_count} 个服务安装失败")
        print("请检查错误信息并重试")
        sys.exit(1)


if __name__ == "__main__":
    main()
