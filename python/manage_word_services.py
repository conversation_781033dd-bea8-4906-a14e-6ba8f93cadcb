#!/usr/bin/env python3
"""
Word格式化服务管理脚本

用于启动、停止和管理Word样式文本服务和Word表格服务
"""
import os
import sys
import subprocess
import signal
import time
from pathlib import Path


class ServiceManager:
    def __init__(self):
        self.services = {
            "style-text": {
                "name": "Word样式文本服务",
                "module": "word_style_text.main",
                "description": "处理样式创建和文本格式化"
            },
            "table": {
                "name": "Word表格服务", 
                "module": "word_table.main",
                "description": "处理表格格式化和布局控制"
            }
        }
        self.processes = {}
    
    def list_services(self):
        """列出所有可用服务"""
        print("📋 可用的Word格式化服务:")
        print("-" * 50)
        for key, service in self.services.items():
            print(f"🔹 {key}: {service['name']}")
            print(f"   {service['description']}")
            print(f"   模块: {service['module']}")
            print()
    
    def start_service(self, service_key):
        """启动指定服务"""
        if service_key not in self.services:
            print(f"❌ 错误: 未知服务 '{service_key}'")
            return False
        
        service = self.services[service_key]
        
        if service_key in self.processes:
            print(f"⚠️  {service['name']} 已经在运行中")
            return True
        
        print(f"🚀 启动 {service['name']}...")
        
        try:
            # 启动服务进程
            process = subprocess.Popen(
                [sys.executable, "-m", service["module"]],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待一小段时间检查进程是否成功启动
            time.sleep(1)
            
            if process.poll() is None:
                self.processes[service_key] = process
                print(f"✅ {service['name']} 启动成功 (PID: {process.pid})")
                return True
            else:
                stdout, stderr = process.communicate()
                print(f"❌ {service['name']} 启动失败:")
                if stderr:
                    print(f"错误: {stderr}")
                if stdout:
                    print(f"输出: {stdout}")
                return False
                
        except Exception as e:
            print(f"❌ 启动 {service['name']} 时发生异常: {e}")
            return False
    
    def stop_service(self, service_key):
        """停止指定服务"""
        if service_key not in self.services:
            print(f"❌ 错误: 未知服务 '{service_key}'")
            return False
        
        service = self.services[service_key]
        
        if service_key not in self.processes:
            print(f"⚠️  {service['name']} 未在运行")
            return True
        
        print(f"🛑 停止 {service['name']}...")
        
        try:
            process = self.processes[service_key]
            process.terminate()
            
            # 等待进程结束
            try:
                process.wait(timeout=5)
                print(f"✅ {service['name']} 已停止")
            except subprocess.TimeoutExpired:
                # 如果进程没有在5秒内结束，强制杀死
                process.kill()
                process.wait()
                print(f"✅ {service['name']} 已强制停止")
            
            del self.processes[service_key]
            return True
            
        except Exception as e:
            print(f"❌ 停止 {service['name']} 时发生异常: {e}")
            return False
    
    def status(self):
        """显示所有服务状态"""
        print("📊 服务状态:")
        print("-" * 50)
        
        for key, service in self.services.items():
            if key in self.processes:
                process = self.processes[key]
                if process.poll() is None:
                    print(f"🟢 {service['name']}: 运行中 (PID: {process.pid})")
                else:
                    print(f"🔴 {service['name']}: 已停止")
                    del self.processes[key]
            else:
                print(f"⚪ {service['name']}: 未启动")
    
    def start_all(self):
        """启动所有服务"""
        print("🚀 启动所有服务...")
        success_count = 0
        
        for key in self.services.keys():
            if self.start_service(key):
                success_count += 1
        
        print(f"\n✅ 成功启动 {success_count}/{len(self.services)} 个服务")
    
    def stop_all(self):
        """停止所有服务"""
        print("🛑 停止所有服务...")
        
        for key in list(self.processes.keys()):
            self.stop_service(key)
        
        print("✅ 所有服务已停止")


def main():
    """主函数"""
    manager = ServiceManager()
    
    if len(sys.argv) < 2:
        print("🔧 Word格式化服务管理器")
        print("=" * 50)
        print("使用方法:")
        print("  python manage_word_services.py <命令> [服务名]")
        print()
        print("命令:")
        print("  list        - 列出所有可用服务")
        print("  start <服务> - 启动指定服务")
        print("  stop <服务>  - 停止指定服务")
        print("  status      - 显示所有服务状态")
        print("  start-all   - 启动所有服务")
        print("  stop-all    - 停止所有服务")
        print()
        print("服务名:")
        print("  style-text  - Word样式文本服务")
        print("  table       - Word表格服务")
        return
    
    command = sys.argv[1].lower()
    
    if command == "list":
        manager.list_services()
    elif command == "status":
        manager.status()
    elif command == "start-all":
        manager.start_all()
    elif command == "stop-all":
        manager.stop_all()
    elif command == "start":
        if len(sys.argv) < 3:
            print("❌ 错误: 请指定要启动的服务")
            return
        manager.start_service(sys.argv[2])
    elif command == "stop":
        if len(sys.argv) < 3:
            print("❌ 错误: 请指定要停止的服务")
            return
        manager.stop_service(sys.argv[2])
    else:
        print(f"❌ 错误: 未知命令 '{command}'")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 用户中断，正在停止所有服务...")
        manager = ServiceManager()
        manager.stop_all()
